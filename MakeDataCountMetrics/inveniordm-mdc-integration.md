# InvenioRDM + Make Data Count Integration for DRH

## Executive Summary

This document outlines the integration strategy for implementing Make Data Count (MDC) metrics in the Diabetes Research Hub (DRH) project using InvenioRDM as the foundational repository platform. InvenioRDM provides native COUNTER-compliant usage statistics and DataCite integration, which we'll extend with custom MDC features for comprehensive data impact tracking.

## InvenioRDM Native MDC Capabilities

### ✅ Built-in Features

**COUNTER-Compliant Usage Statistics (v12+)**
- Automatic tracking of record views and file downloads
- Deduplication of events within 1-second intervals
- Robot detection and filtering
- Unique user counting based on session/IP
- Storage in search indices for high performance

**DataCite Integration**
- Native DOI minting with automatic metadata registration
- Support for concept DOIs (parent DOIs for versioning)
- Test and production environment support
- Configurable DOI formats and prefixes

**REST API Access**
- Statistics endpoints for programmatic access
- Record metadata includes usage statistics
- Search integration for sorting by popularity

### ❌ Missing MDC Components

- Citation detection and tracking
- Make Data Count corpus contribution
- Advanced impact metrics calculation
- Cross-repository citation analysis

## Architecture Overview

```mermaid
graph TB
    subgraph "DRH System"
        A[CGM Data Processing]
        B[Study Management]
        C[Participant Analytics]
        D[Research Workflows]
    end
    
    subgraph "Integration Layer"
        E[DRH-InvenioRDM Bridge]
        F[Metadata Mapper]
        G[Dataset Publisher]
        H[Statistics Enhancer]
    end
    
    subgraph "InvenioRDM Repository"
        I[Dataset Records]
        J[DOI Management]
        K[Usage Statistics]
        L[DataCite Registration]
    end
    
    subgraph "MDC Extensions"
        M[Citation Detector]
        N[Impact Calculator]
        O[Corpus Contributor]
        P[Analytics Dashboard]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> K
    H --> L
    
    I --> M
    J --> N
    K --> O
    L --> P
```

## Integration Strategy: Hybrid Approach

### Phase 1: InvenioRDM Foundation (Weeks 1-6)

**1.1 InvenioRDM Setup**
```bash
# Install InvenioRDM
pip install invenio-cli
invenio-cli init rdm -c v12.0 --flavour=RDM drh-repository
cd drh-repository
invenio-cli install
```

**1.2 DataCite Configuration**
```python
# invenio.cfg
DATACITE_ENABLED = True
DATACITE_USERNAME = "drh_datacite_user"
DATACITE_PASSWORD = os.environ.get('DATACITE_PASSWORD')
DATACITE_PREFIX = "10.5555"  # Your assigned prefix
DATACITE_FORMAT = "{prefix}/drh.{id}"
DATACITE_TEST_MODE = False  # Set to True for testing

# Enable concept DOIs for versioning
RDM_PARENT_PERSISTENT_IDENTIFIERS = {
    "doi": {
        "providers": ["datacite"],
        "required": True,
        "label": _("DOI"),
    }
}
```

**1.3 Usage Statistics Configuration**
```python
# Enable comprehensive statistics tracking
STATS_EVENTS = {
    'record-view': {
        'signal': 'invenio_records_ui.signals.record_viewed',
        'event_builders': [
            'invenio_stats.contrib.event_builders.record_view_event_builder'
        ],
        'cls': RecordViewEvent,
    },
    'file-download': {
        'signal': 'invenio_files_rest.signals.file_downloaded',
        'event_builders': [
            'invenio_stats.contrib.event_builders.file_download_event_builder'
        ],
        'cls': FileDownloadEvent,
    }
}

# Configure aggregation intervals
STATS_AGGREGATIONS = {
    'record-view': {
        'interval': 'day',
        'index_interval': 'month',
    },
    'file-download': {
        'interval': 'day', 
        'index_interval': 'month',
    }
}
```

### Phase 2: DRH Integration Bridge (Weeks 7-10)

**2.1 Metadata Mapping Service**
```python
class DRHDataCiteMapper:
    """Maps DRH study metadata to DataCite schema"""
    
    def map_study_metadata(self, study_data):
        return {
            "titles": [{"title": study_data.title}],
            "creators": self._map_investigators(study_data.investigators),
            "publicationYear": study_data.publication_year,
            "resourceType": {
                "resourceTypeGeneral": "Dataset",
                "resourceType": "CGM Dataset"
            },
            "subjects": [
                {"subject": "Diabetes"},
                {"subject": "Continuous Glucose Monitoring"},
                {"subject": "CGM"},
                {"subject": study_data.diabetes_type}
            ],
            "descriptions": [{
                "description": study_data.description,
                "descriptionType": "Abstract"
            }],
            "geoLocations": self._map_study_locations(study_data),
            "fundingReferences": self._map_funding(study_data.funding),
            "relatedIdentifiers": self._map_related_studies(study_data),
            "sizes": [f"{study_data.participant_count} participants"],
            "formats": ["CSV", "JSON", "SQLite"],
            "version": study_data.version,
            "rightsList": [{
                "rights": "Creative Commons Attribution 4.0 International",
                "rightsURI": "https://creativecommons.org/licenses/by/4.0/"
            }]
        }
    
    def _map_investigators(self, investigators):
        return [
            {
                "name": inv.name,
                "nameType": "Personal",
                "givenName": inv.first_name,
                "familyName": inv.last_name,
                "nameIdentifiers": [
                    {
                        "nameIdentifier": inv.orcid,
                        "nameIdentifierScheme": "ORCID"
                    }
                ] if inv.orcid else [],
                "affiliation": [{"name": inv.institution}]
            }
            for inv in investigators
        ]
```

**2.2 Dataset Publishing Service**
```python
class DRHInvenioPublisher:
    """Publishes DRH datasets to InvenioRDM"""
    
    def __init__(self, invenio_client, mapper):
        self.invenio = invenio_client
        self.mapper = mapper
    
    def publish_study_dataset(self, study_id, include_files=True):
        """Publish a complete study dataset"""
        try:
            # Get study data from DRH
            study_data = self.get_drh_study_data(study_id)
            
            # Map to DataCite metadata
            metadata = self.mapper.map_study_metadata(study_data)
            
            # Create draft record
            draft = self.invenio.create_draft(metadata)
            
            # Upload files if requested
            if include_files:
                self._upload_study_files(draft.id, study_id)
            
            # Publish record (triggers DOI minting)
            published_record = self.invenio.publish_draft(draft.id)
            
            # Update DRH with DOI information
            self._update_drh_study_doi(study_id, published_record)
            
            return published_record
            
        except Exception as e:
            logger.error(f"Failed to publish study {study_id}: {e}")
            raise
    
    def publish_participant_dataset(self, study_id, participant_id):
        """Publish individual participant dataset"""
        participant_data = self.get_participant_data(study_id, participant_id)
        metadata = self.mapper.map_participant_metadata(participant_data)
        
        # Link to parent study
        metadata["relatedIdentifiers"].append({
            "relatedIdentifier": self.get_study_doi(study_id),
            "relatedIdentifierType": "DOI",
            "relationType": "IsPartOf"
        })
        
        return self._create_and_publish(metadata, participant_data.files)
    
    def _upload_study_files(self, draft_id, study_id):
        """Upload study files to InvenioRDM"""
        files = self.get_study_files(study_id)
        
        for file_info in files:
            self.invenio.upload_file(
                draft_id, 
                file_info.path, 
                file_info.filename
            )
```

### Phase 3: MDC Extensions (Weeks 11-16)

**3.1 Citation Detection Service**
```python
class CitationDetectionService:
    """Detects citations to DRH datasets"""
    
    def __init__(self):
        self.crossref_client = CrossrefClient()
        self.pubmed_client = PubMedClient()
        self.semantic_scholar_client = SemanticScholarClient()
    
    async def detect_citations(self, doi):
        """Detect citations across multiple sources"""
        citations = []
        
        # Search Crossref
        crossref_citations = await self._search_crossref(doi)
        citations.extend(crossref_citations)
        
        # Search PubMed
        pubmed_citations = await self._search_pubmed(doi)
        citations.extend(pubmed_citations)
        
        # Search Semantic Scholar
        scholar_citations = await self._search_semantic_scholar(doi)
        citations.extend(scholar_citations)
        
        # Deduplicate and enrich
        return self._deduplicate_citations(citations)
    
    async def _search_crossref(self, doi):
        """Search Crossref for citations"""
        query = f'doi:"{doi}"'
        results = await self.crossref_client.search(query)
        
        return [
            {
                'source': 'crossref',
                'citing_doi': result.get('DOI'),
                'title': result.get('title', [{}])[0].get('title'),
                'authors': self._extract_authors(result.get('author', [])),
                'publication_date': result.get('published-print', {}).get('date-parts', [[]])[0],
                'journal': result.get('container-title', [None])[0],
                'citation_type': 'direct'
            }
            for result in results
        ]
    
    def schedule_periodic_detection(self):
        """Schedule regular citation detection"""
        # Run daily citation detection for all DRH DOIs
        dois = self.get_all_drh_dois()
        
        for doi in dois:
            self.detect_citations.delay(doi)  # Celery task
```

**3.2 Impact Metrics Calculator**
```python
class ImpactMetricsCalculator:
    """Calculates comprehensive impact metrics"""
    
    def calculate_dataset_impact(self, doi):
        """Calculate comprehensive impact metrics for a dataset"""
        
        # Get InvenioRDM native statistics
        invenio_stats = self.get_invenio_statistics(doi)
        
        # Get citation data
        citations = self.get_citation_data(doi)
        
        # Calculate derived metrics
        impact_metrics = {
            'usage_metrics': {
                'total_views': invenio_stats['all_versions']['views'],
                'unique_views': invenio_stats['all_versions']['unique_views'],
                'total_downloads': invenio_stats['all_versions']['downloads'],
                'unique_downloads': invenio_stats['all_versions']['unique_downloads'],
                'data_volume_downloaded': invenio_stats['all_versions']['data_volume']
            },
            'citation_metrics': {
                'total_citations': len(citations),
                'direct_citations': len([c for c in citations if c['type'] == 'direct']),
                'indirect_citations': len([c for c in citations if c['type'] == 'indirect']),
                'h_index': self._calculate_h_index(citations),
                'citation_velocity': self._calculate_citation_velocity(citations)
            },
            'reach_metrics': {
                'countries_reached': self._count_unique_countries(invenio_stats),
                'institutions_reached': self._count_unique_institutions(citations),
                'research_domains': self._identify_research_domains(citations),
                'collaboration_index': self._calculate_collaboration_index(citations)
            },
            'temporal_metrics': {
                'days_since_publication': self._days_since_publication(doi),
                'peak_usage_period': self._identify_peak_usage(invenio_stats),
                'citation_timeline': self._create_citation_timeline(citations)
            },
            'composite_scores': {
                'impact_score': self._calculate_impact_score(invenio_stats, citations),
                'reuse_score': self._calculate_reuse_score(citations),
                'reach_score': self._calculate_reach_score(invenio_stats, citations)
            }
        }
        
        return impact_metrics
    
    def _calculate_impact_score(self, usage_stats, citations):
        """Calculate composite impact score (0-100)"""
        # Weighted combination of usage and citation metrics
        usage_weight = 0.4
        citation_weight = 0.6
        
        usage_score = min(100, (usage_stats['all_versions']['views'] / 1000) * 50)
        citation_score = min(100, len(citations) * 10)
        
        return (usage_score * usage_weight) + (citation_score * citation_weight)
```

### Phase 4: Dashboard and Reporting (Weeks 17-20)

**4.1 Impact Dashboard Component**
```typescript
// web-components/src/components/drh-impact-dashboard.ts
import { LitElement, html, css } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';

@customElement('drh-impact-dashboard')
export class DRHImpactDashboard extends LitElement {
  @property({ type: String }) studyId = '';
  @property({ type: String }) doi = '';
  @state() private impactData: any = {};
  @state() private loading = true;

  static styles = css`
    .dashboard-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      padding: 1rem;
    }
    
    .metric-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #2563eb;
    }
    
    .metric-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 1rem;
    }
    
    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #2563eb;
      margin-bottom: 0.5rem;
    }
    
    .metric-label {
      font-size: 0.875rem;
      color: #6b7280;
    }
    
    .trend-indicator {
      display: inline-flex;
      align-items: center;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }
    
    .trend-up { color: #10b981; }
    .trend-down { color: #ef4444; }
    
    .chart-container {
      grid-column: span 2;
      height: 300px;
    }
  `;

  render() {
    if (this.loading) {
      return html`<div class="loading">Loading impact metrics...</div>`;
    }

    return html`
      <div class="dashboard-container">
        <!-- Usage Metrics -->
        <div class="metric-card">
          <div class="metric-title">Dataset Views</div>
          <div class="metric-value">${this.formatNumber(this.impactData.usage_metrics?.total_views || 0)}</div>
          <div class="metric-label">Total views across all versions</div>
          <div class="trend-indicator trend-up">
            ↗ ${this.impactData.usage_metrics?.view_trend || '+0%'} this month
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-title">Downloads</div>
          <div class="metric-value">${this.formatNumber(this.impactData.usage_metrics?.total_downloads || 0)}</div>
          <div class="metric-label">Unique downloads</div>
          <div class="trend-indicator trend-up">
            ↗ ${this.impactData.usage_metrics?.download_trend || '+0%'} this month
          </div>
        </div>

        <!-- Citation Metrics -->
        <div class="metric-card">
          <div class="metric-title">Citations</div>
          <div class="metric-value">${this.impactData.citation_metrics?.total_citations || 0}</div>
          <div class="metric-label">Academic citations detected</div>
          <div class="trend-indicator trend-up">
            +${this.impactData.citation_metrics?.new_citations || 0} this month
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-title">Impact Score</div>
          <div class="metric-value">${this.impactData.composite_scores?.impact_score?.toFixed(1) || '0.0'}</div>
          <div class="metric-label">Composite impact rating (0-100)</div>
        </div>

        <!-- Reach Metrics -->
        <div class="metric-card">
          <div class="metric-title">Global Reach</div>
          <div class="metric-value">${this.impactData.reach_metrics?.countries_reached || 0}</div>
          <div class="metric-label">Countries accessing data</div>
        </div>

        <div class="metric-card">
          <div class="metric-title">Research Domains</div>
          <div class="metric-value">${this.impactData.reach_metrics?.research_domains?.length || 0}</div>
          <div class="metric-label">Different research areas</div>
        </div>

        <!-- Usage Timeline Chart -->
        <div class="metric-card chart-container">
          <div class="metric-title">Usage Timeline</div>
          <canvas id="usage-timeline-chart"></canvas>
        </div>
      </div>
    `;
  }

  async connectedCallback() {
    super.connectedCallback();
    await this.loadImpactData();
  }

  private async loadImpactData() {
    try {
      const response = await fetch(`/api/drh/impact/${this.studyId || this.doi}`);
      this.impactData = await response.json();
      this.loading = false;
      this.requestUpdate();
      
      // Initialize charts after data loads
      this.initializeCharts();
    } catch (error) {
      console.error('Failed to load impact data:', error);
      this.loading = false;
    }
  }

  private formatNumber(num: number): string {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  }

  private initializeCharts() {
    // Initialize Chart.js timeline chart
    // Implementation details...
  }
}
```

## Deployment Configuration

### Docker Compose Setup
```yaml
# docker-compose.drh-invenio.yml
version: '3.8'

services:
  invenio-web:
    build: 
      context: ./invenio-instance
      dockerfile: Dockerfile
    environment:
      - INVENIO_SECRET_KEY=${INVENIO_SECRET_KEY}
      - DATACITE_USERNAME=${DATACITE_USERNAME}
      - DATACITE_PASSWORD=${DATACITE_PASSWORD}
      - DATACITE_PREFIX=${DATACITE_PREFIX}
    volumes:
      - invenio-data:/opt/invenio/var/instance
    depends_on:
      - db
      - redis
      - elasticsearch
    ports:
      - "5000:5000"

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=invenio
      - POSTGRES_USER=invenio
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:6
    volumes:
      - redis-data:/data

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data

  # MDC Extensions
  citation-detector:
    build:
      context: ./mdc-extensions
      dockerfile: Dockerfile.citation-detector
    environment:
      - CROSSREF_API_KEY=${CROSSREF_API_KEY}
      - PUBMED_API_KEY=${PUBMED_API_KEY}
    depends_on:
      - db
      - redis

  impact-calculator:
    build:
      context: ./mdc-extensions  
      dockerfile: Dockerfile.impact-calculator
    depends_on:
      - db
      - redis

volumes:
  invenio-data:
  postgres-data:
  redis-data:
  elasticsearch-data:
```

## Benefits of This Integration

### Immediate Benefits (InvenioRDM Foundation)
- **COUNTER-compliant usage statistics** out of the box
- **Automatic DOI minting** and DataCite registration
- **Proven scalability** (powers Zenodo with 2M+ records)
- **Rich metadata support** with DataCite schema
- **REST API access** for programmatic integration

### Enhanced Benefits (MDC Extensions)
- **Comprehensive citation tracking** across academic databases
- **Advanced impact metrics** beyond basic usage statistics
- **Make Data Count corpus contribution** for global visibility
- **Researcher impact profiles** and institutional reporting
- **Cross-study analytics** and collaboration insights

### Long-term Benefits
- **Community adoption** through established repository platform
- **Continuous updates** from active InvenioRDM development
- **Integration ecosystem** with other research tools
- **Standards compliance** with evolving data metrics practices

## Success Metrics

### Technical Metrics
- DOI minting success rate: >99%
- Usage statistics collection: 100% of interactions
- Citation detection accuracy: >95%
- API response times: <500ms

### Research Impact Metrics
- Number of datasets published: Target 100+ studies
- Citation growth rate: 20% quarterly increase
- Global reach: 50+ countries accessing data
- Researcher adoption: 80% of DRH investigators participating

### Community Engagement
- Make Data Count corpus contributions: Monthly submissions
- Cross-institutional collaborations: 10+ new partnerships
- Funder reporting compliance: 100% of required reports

This integration strategy provides a robust foundation for Make Data Count implementation while leveraging proven repository infrastructure and enabling future enhancements as the MDC ecosystem evolves.
