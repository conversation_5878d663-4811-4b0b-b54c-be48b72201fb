# DRH-InvenioRDM Integration Bridge

## Overview

This document details the implementation of the integration bridge between the
Diabetes Research Hub (DRH) system and InvenioRDM repository. The bridge enables
automatic dataset publishing, metadata synchronization, and bidirectional data
flow between the two systems.

## Architecture

```mermaid
graph TB
    subgraph "DRH System"
        A[Study Management]
        B[CGM Data Processing]
        C[Participant Management]
        D[Analytics Engine]
    end

    subgraph "Integration Bridge"
        E[Data Synchronizer]
        F[Metadata Mapper]
        G[File Packager]
        H[Status Monitor]
    end

    subgraph "InvenioRDM Repository"
        I[Record Management]
        J[DOI Service]
        K[File Storage]
        L[Usage Statistics]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L
```

## Core Components

### 1. Data Synchronization Service

The synchronization service manages the flow of data between DRH and InvenioRDM,
ensuring consistency and handling conflicts.

```java
@Service
@Slf4j
public class DRHInvenioSyncService {

    private final InvenioRDMClient invenioClient;
    private final DRHDataService drhDataService;
    private final MetadataMapper metadataMapper;
    private final FilePackager filePackager;

    @Autowired
    public DRHInvenioSyncService(
            InvenioRDMClient invenioClient,
            DRHDataService drhDataService,
            MetadataMapper metadataMapper,
            FilePackager filePackager) {
        this.invenioClient = invenioClient;
        this.drhDataService = drhDataService;
        this.metadataMapper = metadataMapper;
        this.filePackager = filePackager;
    }

    @Async
    public CompletableFuture<PublicationResult> publishStudyDataset(String studyId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Starting publication process for study: {}", studyId);

                // 1. Validate study data
                StudyData studyData = validateAndRetrieveStudyData(studyId);

                // 2. Map metadata to DataCite schema
                DataCiteMetadata metadata = metadataMapper.mapStudyToDataCite(studyData);

                // 3. Package files for upload
                FilePackage filePackage = filePackager.packageStudyFiles(studyId);

                // 4. Create draft in InvenioRDM
                InvenioRecord draft = invenioClient.createDraft(metadata);

                // 5. Upload files
                uploadFiles(draft.getId(), filePackage);

                // 6. Publish record (triggers DOI minting)
                InvenioRecord publishedRecord = invenioClient.publishDraft(draft.getId());

                // 7. Update DRH with publication details
                updateDRHWithPublicationInfo(studyId, publishedRecord);

                log.info("Successfully published study {} with DOI: {}",
                        studyId, publishedRecord.getDoi());

                return PublicationResult.success(publishedRecord);

            } catch (Exception e) {
                log.error("Failed to publish study {}: {}", studyId, e.getMessage(), e);
                return PublicationResult.failure(e);
            }
        });
    }

    private StudyData validateAndRetrieveStudyData(String studyId) {
        StudyData studyData = drhDataService.getStudyData(studyId);

        if (studyData == null) {
            throw new IllegalArgumentException("Study not found: " + studyId);
        }

        if (!studyData.isReadyForPublication()) {
            throw new IllegalStateException("Study not ready for publication: " + studyId);
        }

        return studyData;
    }

    private void uploadFiles(String draftId, FilePackage filePackage) {
        for (FileInfo file : filePackage.getFiles()) {
            try {
                invenioClient.uploadFile(draftId, file.getPath(), file.getFilename());
                log.debug("Uploaded file: {} for draft: {}", file.getFilename(), draftId);
            } catch (Exception e) {
                log.error("Failed to upload file {}: {}", file.getFilename(), e.getMessage());
                throw new RuntimeException("File upload failed", e);
            }
        }
    }

    private void updateDRHWithPublicationInfo(String studyId, InvenioRecord record) {
        PublicationInfo pubInfo = PublicationInfo.builder()
                .studyId(studyId)
                .invenioRecordId(record.getId())
                .doi(record.getDoi())
                .publicationDate(record.getPublicationDate())
                .repositoryUrl(record.getUrl())
                .status(PublicationStatus.PUBLISHED)
                .build();

        drhDataService.updatePublicationInfo(studyId, pubInfo);
    }
}
```

### 2. Metadata Mapping Service

Converts DRH study metadata to DataCite-compliant format for InvenioRDM.

```java
@Component
@Slf4j
public class MetadataMapper {

    private final ObjectMapper objectMapper;

    public MetadataMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public DataCiteMetadata mapStudyToDataCite(StudyData studyData) {
        return DataCiteMetadata.builder()
                .titles(mapTitles(studyData))
                .creators(mapCreators(studyData.getInvestigators()))
                .publicationYear(studyData.getPublicationYear())
                .resourceType(mapResourceType())
                .subjects(mapSubjects(studyData))
                .descriptions(mapDescriptions(studyData))
                .contributors(mapContributors(studyData))
                .dates(mapDates(studyData))
                .language("en")
                .alternateIdentifiers(mapAlternateIdentifiers(studyData))
                .relatedIdentifiers(mapRelatedIdentifiers(studyData))
                .sizes(mapSizes(studyData))
                .formats(mapFormats())
                .version(studyData.getVersion())
                .rightsList(mapRights())
                .fundingReferences(mapFunding(studyData.getFunding()))
                .geoLocations(mapGeoLocations(studyData))
                .build();
    }

    private List<Title> mapTitles(StudyData studyData) {
        List<Title> titles = new ArrayList<>();

        // Main title
        titles.add(Title.builder()
                .title(studyData.getTitle())
                .titleType(null) // Main title has no type
                .build());

        // Subtitle if available
        if (studyData.getSubtitle() != null) {
            titles.add(Title.builder()
                    .title(studyData.getSubtitle())
                    .titleType("Subtitle")
                    .build());
        }

        return titles;
    }

    private List<Creator> mapCreators(List<Investigator> investigators) {
        return investigators.stream()
                .filter(inv -> inv.getRole().equals("Principal Investigator") ||
                              inv.getRole().equals("Co-Investigator"))
                .map(this::mapInvestigatorToCreator)
                .collect(Collectors.toList());
    }

    private Creator mapInvestigatorToCreator(Investigator investigator) {
        return Creator.builder()
                .name(investigator.getFullName())
                .nameType("Personal")
                .givenName(investigator.getFirstName())
                .familyName(investigator.getLastName())
                .nameIdentifiers(mapNameIdentifiers(investigator))
                .affiliation(mapAffiliations(investigator))
                .build();
    }

    private List<NameIdentifier> mapNameIdentifiers(Investigator investigator) {
        List<NameIdentifier> identifiers = new ArrayList<>();

        if (investigator.getOrcid() != null) {
            identifiers.add(NameIdentifier.builder()
                    .nameIdentifier(investigator.getOrcid())
                    .nameIdentifierScheme("ORCID")
                    .schemeURI("https://orcid.org/")
                    .build());
        }

        return identifiers;
    }

    private List<Subject> mapSubjects(StudyData studyData) {
        List<Subject> subjects = new ArrayList<>();

        // Standard diabetes research subjects
        subjects.add(Subject.builder().subject("Diabetes").build());
        subjects.add(Subject.builder().subject("Continuous Glucose Monitoring").build());
        subjects.add(Subject.builder().subject("CGM").build());

        // Study-specific subjects
        if (studyData.getDiabetesType() != null) {
            subjects.add(Subject.builder()
                    .subject(studyData.getDiabetesType())
                    .build());
        }

        // Add custom keywords
        if (studyData.getKeywords() != null) {
            studyData.getKeywords().forEach(keyword ->
                    subjects.add(Subject.builder().subject(keyword).build()));
        }

        return subjects;
    }

    private ResourceType mapResourceType() {
        return ResourceType.builder()
                .resourceTypeGeneral("Dataset")
                .resourceType("CGM Dataset")
                .build();
    }

    private List<Description> mapDescriptions(StudyData studyData) {
        List<Description> descriptions = new ArrayList<>();

        // Abstract
        if (studyData.getAbstract() != null) {
            descriptions.add(Description.builder()
                    .description(studyData.getAbstract())
                    .descriptionType("Abstract")
                    .build());
        }

        // Methods
        if (studyData.getMethods() != null) {
            descriptions.add(Description.builder()
                    .description(studyData.getMethods())
                    .descriptionType("Methods")
                    .build());
        }

        // Technical info
        String technicalInfo = buildTechnicalInfo(studyData);
        descriptions.add(Description.builder()
                .description(technicalInfo)
                .descriptionType("TechnicalInfo")
                .build());

        return descriptions;
    }

    private String buildTechnicalInfo(StudyData studyData) {
        StringBuilder techInfo = new StringBuilder();

        techInfo.append("Study Details:\n");
        techInfo.append("- Participants: ").append(studyData.getParticipantCount()).append("\n");
        techInfo.append("- Duration: ").append(studyData.getStudyDuration()).append("\n");
        techInfo.append("- CGM Device: ").append(studyData.getCgmDevice()).append("\n");
        techInfo.append("- Data Points: ").append(studyData.getDataPointCount()).append("\n");

        if (studyData.getInclusionCriteria() != null) {
            techInfo.append("- Inclusion Criteria: ").append(studyData.getInclusionCriteria()).append("\n");
        }

        if (studyData.getExclusionCriteria() != null) {
            techInfo.append("- Exclusion Criteria: ").append(studyData.getExclusionCriteria()).append("\n");
        }

        return techInfo.toString();
    }

    private List<FundingReference> mapFunding(List<FundingInfo> fundingList) {
        return fundingList.stream()
                .map(funding -> FundingReference.builder()
                        .funderName(funding.getFunderName())
                        .funderIdentifier(funding.getFunderIdentifier())
                        .funderIdentifierType(funding.getIdentifierType())
                        .awardNumber(funding.getGrantNumber())
                        .awardTitle(funding.getProjectTitle())
                        .build())
                .collect(Collectors.toList());
    }
}
```

### 3. File Packaging Service

Handles the preparation and packaging of study files for upload to InvenioRDM.

```java
@Service
@Slf4j
public class FilePackager {

    private final DRHDataService drhDataService;
    private final FileCompressionService compressionService;
    private final DataExportService exportService;

    public FilePackage packageStudyFiles(String studyId) {
        log.info("Packaging files for study: {}", studyId);

        FilePackage.Builder packageBuilder = FilePackage.builder();

        try {
            // 1. Export CGM data
            File cgmDataFile = exportCGMData(studyId);
            packageBuilder.addFile(FileInfo.builder()
                    .filename("cgm_data.csv")
                    .path(cgmDataFile.getAbsolutePath())
                    .description("Continuous glucose monitoring data")
                    .fileType("text/csv")
                    .build());

            // 2. Export participant demographics
            File demographicsFile = exportParticipantDemographics(studyId);
            packageBuilder.addFile(FileInfo.builder()
                    .filename("participant_demographics.csv")
                    .path(demographicsFile.getAbsolutePath())
                    .description("Participant demographic information")
                    .fileType("text/csv")
                    .build());

            // 3. Export study metadata
            File metadataFile = exportStudyMetadata(studyId);
            packageBuilder.addFile(FileInfo.builder()
                    .filename("study_metadata.json")
                    .path(metadataFile.getAbsolutePath())
                    .description("Complete study metadata")
                    .fileType("application/json")
                    .build());

            // 4. Create data dictionary
            File dataDictionaryFile = createDataDictionary(studyId);
            packageBuilder.addFile(FileInfo.builder()
                    .filename("data_dictionary.pdf")
                    .path(dataDictionaryFile.getAbsolutePath())
                    .description("Data dictionary and variable definitions")
                    .fileType("application/pdf")
                    .build());

            // 5. Add analysis scripts if available
            List<File> analysisScripts = getAnalysisScripts(studyId);
            for (File script : analysisScripts) {
                packageBuilder.addFile(FileInfo.builder()
                        .filename(script.getName())
                        .path(script.getAbsolutePath())
                        .description("Analysis script")
                        .fileType(determineFileType(script))
                        .build());
            }

            // 6. Create README file
            File readmeFile = createReadmeFile(studyId);
            packageBuilder.addFile(FileInfo.builder()
                    .filename("README.md")
                    .path(readmeFile.getAbsolutePath())
                    .description("Dataset documentation and usage instructions")
                    .fileType("text/markdown")
                    .build());

            FilePackage filePackage = packageBuilder.build();
            log.info("Successfully packaged {} files for study: {}",
                    filePackage.getFiles().size(), studyId);

            return filePackage;

        } catch (Exception e) {
            log.error("Failed to package files for study {}: {}", studyId, e.getMessage(), e);
            throw new RuntimeException("File packaging failed", e);
        }
    }

    private File exportCGMData(String studyId) throws IOException {
        log.debug("Exporting CGM data for study: {}", studyId);

        List<CGMReading> cgmData = drhDataService.getCGMData(studyId);
        File outputFile = File.createTempFile("cgm_data_" + studyId, ".csv");

        try (FileWriter writer = new FileWriter(outputFile);
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT
                     .withHeader("participant_id", "timestamp", "glucose_value", "device_id"))) {

            for (CGMReading reading : cgmData) {
                csvPrinter.printRecord(
                        reading.getParticipantId(),
                        reading.getTimestamp().toString(),
                        reading.getGlucoseValue(),
                        reading.getDeviceId()
                );
            }
        }

        log.debug("Exported {} CGM readings to: {}", cgmData.size(), outputFile.getAbsolutePath());
        return outputFile;
    }

    private File createReadmeFile(String studyId) throws IOException {
        StudyData studyData = drhDataService.getStudyData(studyId);
        File readmeFile = File.createTempFile("README_" + studyId, ".md");

        try (FileWriter writer = new FileWriter(readmeFile)) {
            writer.write(generateReadmeContent(studyData));
        }

        return readmeFile;
    }

    private String generateReadmeContent(StudyData studyData) {
        StringBuilder readme = new StringBuilder();

        readme.append("# ").append(studyData.getTitle()).append("\n\n");

        readme.append("## Study Overview\n");
        readme.append(studyData.getAbstract()).append("\n\n");

        readme.append("## Dataset Contents\n");
        readme.append("This dataset contains the following files:\n\n");
        readme.append("- `cgm_data.csv`: Continuous glucose monitoring readings\n");
        readme.append("- `participant_demographics.csv`: Participant demographic information\n");
        readme.append("- `study_metadata.json`: Complete study metadata\n");
        readme.append("- `data_dictionary.pdf`: Variable definitions and data dictionary\n\n");

        readme.append("## Data Format\n");
        readme.append("### CGM Data\n");
        readme.append("The CGM data file contains the following columns:\n");
        readme.append("- `participant_id`: Unique participant identifier\n");
        readme.append("- `timestamp`: Date and time of reading (ISO 8601 format)\n");
        readme.append("- `glucose_value`: Glucose reading in mg/dL\n");
        readme.append("- `device_id`: CGM device identifier\n\n");

        readme.append("## Usage Guidelines\n");
        readme.append("Please cite this dataset using the DOI provided. ");
        readme.append("For questions about the data, contact the study investigators.\n\n");

        readme.append("## License\n");
        readme.append("This dataset is licensed under Creative Commons Attribution 4.0 International (CC BY 4.0).\n");

        return readme.toString();
    }
}
```

This integration bridge provides a robust foundation for connecting DRH with
InvenioRDM, enabling seamless dataset publication and metadata synchronization
while maintaining data integrity and providing comprehensive documentation.
