# InvenioRDM Setup Guide for DRH

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+ recommended) or macOS
- **Python**: 3.9 or 3.10 (3.11+ not yet supported)
- **Node.js**: 16.x or 18.x
- **Docker**: 20.10+ and Docker Compose 2.0+
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 50GB+ available disk space

### Required Accounts
- **DataCite Account**: For DOI minting ([Get test account](https://support.datacite.org/docs/getting-a-test-account))
- **ORCID Account**: For researcher identification
- **GitHub Account**: For code repository access

## Installation Steps

### Step 1: Environment Setup

```bash
# Create project directory
mkdir drh-invenio-repository
cd drh-invenio-repository

# Create Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Install InvenioRDM CLI
pip install invenio-cli
```

### Step 2: Initialize InvenioRDM Project

```bash
# Initialize new InvenioRDM instance
invenio-cli init rdm -c v12.0 --flavour=RDM drh-repository

# Navigate to project directory
cd drh-repository

# Install dependencies
invenio-cli install

# Build assets
invenio-cli assets build
```

### Step 3: Configuration

#### 3.1 Basic Configuration (`invenio.cfg`)

```python
# -*- coding: utf-8 -*-
#
# DRH InvenioRDM Configuration

import os
from datetime import timedelta

# Site Information
SITE_NAME = "Diabetes Research Hub Repository"
SITE_URL = "https://data.diabetes-research.org"
SITE_HOSTNAME = "data.diabetes-research.org"

# Security
SECRET_KEY = os.environ.get('INVENIO_SECRET_KEY', 'CHANGE_ME')
SECURITY_PASSWORD_SALT = os.environ.get('INVENIO_SECURITY_PASSWORD_SALT', 'CHANGE_ME')

# Database
SQLALCHEMY_DATABASE_URI = os.environ.get(
    'SQLALCHEMY_DATABASE_URI',
    'postgresql+psycopg2://invenio:invenio@localhost/invenio'
)

# Redis
CACHE_REDIS_URL = os.environ.get('CACHE_REDIS_URL', 'redis://localhost:6379/0')
ACCOUNTS_SESSION_REDIS_URL = os.environ.get('ACCOUNTS_SESSION_REDIS_URL', 'redis://localhost:6379/1')
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/2')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/3')

# Elasticsearch
SEARCH_ELASTIC_HOSTS = [os.environ.get('SEARCH_ELASTIC_HOSTS', 'localhost:9200')]

# DataCite Configuration
DATACITE_ENABLED = True
DATACITE_USERNAME = os.environ.get('DATACITE_USERNAME')
DATACITE_PASSWORD = os.environ.get('DATACITE_PASSWORD')
DATACITE_PREFIX = os.environ.get('DATACITE_PREFIX', '10.5555')
DATACITE_TEST_MODE = os.environ.get('DATACITE_TEST_MODE', 'True').lower() == 'true'
DATACITE_FORMAT = "{prefix}/drh.{id}"
DATACITE_DATACENTER_SYMBOL = "DRH.REPOSITORY"

# Enable parent DOIs for versioning
RDM_PARENT_PERSISTENT_IDENTIFIERS = {
    "doi": {
        "providers": ["datacite"],
        "required": True,
        "label": "DOI",
    }
}

# Usage Statistics Configuration
STATS_EVENTS = {
    'record-view': {
        'signal': 'invenio_records_ui.signals.record_viewed',
        'event_builders': [
            'invenio_stats.contrib.event_builders.record_view_event_builder'
        ],
    },
    'file-download': {
        'signal': 'invenio_files_rest.signals.file_downloaded',
        'event_builders': [
            'invenio_stats.contrib.event_builders.file_download_event_builder'
        ],
    }
}

# Email Configuration
MAIL_SERVER = os.environ.get('MAIL_SERVER', 'localhost')
MAIL_PORT = int(os.environ.get('MAIL_PORT', 25))
MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'False').lower() == 'true'
MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False').lower() == 'true'
MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')

# File Storage
FILES_REST_STORAGE_FACTORY = 'invenio_files_rest.storage.pyfs:pyfs_storage_factory'
FILES_REST_DEFAULT_STORAGE_CLASS = 'S'  # Use 'L' for local, 'S' for S3

# S3 Configuration (if using S3)
if os.environ.get('S3_ENABLED', 'False').lower() == 'true':
    FILES_REST_STORAGE_FACTORY = 'invenio_s3.storage:s3fs_storage_factory'
    S3_ENDPOINT_URL = os.environ.get('S3_ENDPOINT_URL')
    S3_ACCESS_KEY_ID = os.environ.get('S3_ACCESS_KEY_ID')
    S3_SECRET_ACCESS_KEY = os.environ.get('S3_SECRET_ACCESS_KEY')
    S3_BUCKET = os.environ.get('S3_BUCKET', 'drh-repository')

# Logging
LOGGING_SENTRY_LEVEL = "WARNING"
SENTRY_DSN = os.environ.get('SENTRY_DSN')

# Rate Limiting
RATELIMIT_STORAGE_URL = os.environ.get('RATELIMIT_STORAGE_URL', 'redis://localhost:6379/4')

# Theme and Branding
THEME_LOGO = "images/drh-logo.png"
THEME_FRONTPAGE_TITLE = "Diabetes Research Hub Data Repository"
THEME_FRONTPAGE_TEMPLATE = "drh_theme/frontpage.html"

# Custom vocabularies for diabetes research
RDM_CUSTOM_VOCABULARIES = {
    'diabetes_types': {
        'pid-type': 'dt',
        'data-file': 'vocabularies/diabetes_types.yaml'
    },
    'cgm_devices': {
        'pid-type': 'cgm',
        'data-file': 'vocabularies/cgm_devices.yaml'
    }
}

# Community features
COMMUNITIES_ENABLED = True
REQUESTS_ENABLED = True

# OAI-PMH
OAISERVER_ID_PREFIX = "oai:data.diabetes-research.org:"
```

#### 3.2 Environment Variables (`.env`)

```bash
# Database
SQLALCHEMY_DATABASE_URI=postgresql+psycopg2://invenio:password@localhost/invenio

# Redis
CACHE_REDIS_URL=redis://localhost:6379/0
ACCOUNTS_SESSION_REDIS_URL=redis://localhost:6379/1
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/3
RATELIMIT_STORAGE_URL=redis://localhost:6379/4

# Elasticsearch
SEARCH_ELASTIC_HOSTS=localhost:9200

# Security
INVENIO_SECRET_KEY=your-very-long-secret-key-here
INVENIO_SECURITY_PASSWORD_SALT=your-password-salt-here

# DataCite
DATACITE_USERNAME=your_datacite_username
DATACITE_PASSWORD=your_datacite_password
DATACITE_PREFIX=10.5555
DATACITE_TEST_MODE=true

# Email
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# S3 Storage (optional)
S3_ENABLED=false
S3_ENDPOINT_URL=https://s3.amazonaws.com
S3_ACCESS_KEY_ID=your_access_key
S3_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET=drh-repository

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
```

### Step 4: Database and Services Setup

#### 4.1 Docker Compose for Services

```yaml
# docker-compose.services.yml
version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=invenio
      - POSTGRES_USER=invenio
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
```

#### 4.2 Start Services

```bash
# Start services
docker-compose -f docker-compose.services.yml up -d

# Wait for services to be ready
sleep 30

# Check service status
docker-compose -f docker-compose.services.yml ps
```

### Step 5: Initialize Application

```bash
# Setup database
invenio-cli services setup

# Create admin user
invenio-cli <NAME_EMAIL> --password=admin123 --active --confirm

# Grant admin role
invenio-cli <NAME_EMAIL> admin

# Create demo data (optional)
invenio-cli demo --users --records
```

### Step 6: Custom Vocabularies

#### 6.1 Diabetes Types Vocabulary (`app_data/vocabularies/diabetes_types.yaml`)

```yaml
- id: type1
  title:
    en: Type 1 Diabetes
  props:
    description: Autoimmune diabetes requiring insulin therapy
    
- id: type2
  title:
    en: Type 2 Diabetes
  props:
    description: Insulin resistance and relative insulin deficiency
    
- id: gestational
  title:
    en: Gestational Diabetes
  props:
    description: Diabetes diagnosed during pregnancy
    
- id: mody
  title:
    en: MODY (Maturity-Onset Diabetes of the Young)
  props:
    description: Monogenic diabetes with early onset
    
- id: secondary
  title:
    en: Secondary Diabetes
  props:
    description: Diabetes due to other medical conditions
```

#### 6.2 CGM Devices Vocabulary (`app_data/vocabularies/cgm_devices.yaml`)

```yaml
- id: dexcom_g6
  title:
    en: Dexcom G6
  props:
    manufacturer: Dexcom
    accuracy: "±9% MARD"
    
- id: dexcom_g7
  title:
    en: Dexcom G7
  props:
    manufacturer: Dexcom
    accuracy: "±8.2% MARD"
    
- id: freestyle_libre
  title:
    en: FreeStyle Libre
  props:
    manufacturer: Abbott
    accuracy: "±9.3% MARD"
    
- id: freestyle_libre2
  title:
    en: FreeStyle Libre 2
  props:
    manufacturer: Abbott
    accuracy: "±9.3% MARD"
    
- id: medtronic_guardian
  title:
    en: Guardian Connect
  props:
    manufacturer: Medtronic
    accuracy: "±9.1% MARD"
```

### Step 7: Custom Theme Setup

#### 7.1 Create Theme Structure

```bash
# Create theme directories
mkdir -p assets/less/drh_theme
mkdir -p templates/drh_theme
mkdir -p static/images

# Copy DRH logo
cp /path/to/drh-logo.png static/images/
```

#### 7.2 Custom Frontpage Template (`templates/drh_theme/frontpage.html`)

```html
{%- extends "invenio_theme/frontpage.html" %}

{%- block page_header %}
<div class="frontpage-header">
  <div class="container">
    <div class="row">
      <div class="col-md-8">
        <h1>{{ config.THEME_FRONTPAGE_TITLE }}</h1>
        <p class="lead">
          Advancing diabetes research through open data sharing and collaboration.
          Discover, access, and cite high-quality diabetes research datasets.
        </p>
        <div class="frontpage-stats">
          <div class="stat-item">
            <span class="stat-number">{{ stats.records }}</span>
            <span class="stat-label">Datasets</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.downloads }}</span>
            <span class="stat-label">Downloads</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ stats.citations }}</span>
            <span class="stat-label">Citations</span>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="frontpage-search">
          <form action="{{ url_for('invenio_search_ui.search') }}" method="GET">
            <div class="input-group">
              <input type="text" class="form-control" name="q" 
                     placeholder="Search diabetes datasets...">
              <div class="input-group-append">
                <button class="btn btn-primary" type="submit">
                  <i class="fa fa-search"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{%- endblock page_header %}

{%- block page_body %}
<div class="frontpage-body">
  <div class="container">
    <div class="row">
      <div class="col-md-4">
        <div class="feature-box">
          <i class="fa fa-database fa-3x"></i>
          <h3>Rich Datasets</h3>
          <p>Access comprehensive CGM data, clinical measurements, and research outcomes from diabetes studies worldwide.</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="feature-box">
          <i class="fa fa-quote-left fa-3x"></i>
          <h3>Citable Research</h3>
          <p>All datasets receive DOIs for proper citation and impact tracking through Make Data Count metrics.</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="feature-box">
          <i class="fa fa-users fa-3x"></i>
          <h3>Open Collaboration</h3>
          <p>Connect with researchers, share methodologies, and advance diabetes research through open science.</p>
        </div>
      </div>
    </div>
  </div>
</div>
{%- endblock page_body %}
```

### Step 8: Start Application

```bash
# Start the application
invenio-cli run

# In separate terminals, start background services
invenio-cli services start

# Start Celery worker for background tasks
celery -A invenio_app.celery worker -l info

# Start Celery beat for scheduled tasks
celery -A invenio_app.celery beat -l info
```

### Step 9: Verification

#### 9.1 Access Application
- **Web Interface**: http://localhost:5000
- **Admin Panel**: http://localhost:5000/administration
- **API Documentation**: http://localhost:5000/api/docs

#### 9.2 Test DOI Minting

```bash
# Create test record via CLI
invenio-cli shell

# In Python shell:
from invenio_rdm_records.proxies import current_rdm_records_service
from invenio_rdm_records.services.schemas import RDMRecordSchema

# Create minimal record
data = {
    "metadata": {
        "title": "Test Diabetes Dataset",
        "creators": [{"person_or_org": {"name": "Test, User"}}],
        "publication_date": "2024-01-01",
        "resource_type": {"id": "dataset"}
    }
}

# Create and publish record
draft = current_rdm_records_service.create(identity=system_identity, data=data)
record = current_rdm_records_service.publish(identity=system_identity, id=draft.id)

print(f"Created record with DOI: {record.data['pids']['doi']['identifier']}")
```

### Step 10: Production Deployment

#### 10.1 Production Configuration Updates

```python
# Production settings in invenio.cfg
DEBUG = False
TESTING = False

# Use production DataCite
DATACITE_TEST_MODE = False

# Enable SSL
PREFERRED_URL_SCHEME = 'https'
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True

# Production database
SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')

# Production cache
CACHE_TYPE = 'redis'
CACHE_REDIS_URL = os.environ.get('REDIS_URL')

# File storage (use S3 in production)
FILES_REST_STORAGE_FACTORY = 'invenio_s3.storage:s3fs_storage_factory'
```

#### 10.2 Docker Production Setup

```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /opt/invenio

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Copy application
COPY . .

# Install Python dependencies
RUN pip install -e .

# Build assets
RUN invenio collect -v
RUN invenio webpack buildall

# Expose port
EXPOSE 5000

# Start command
CMD ["invenio", "run", "--host", "0.0.0.0"]
```

## Troubleshooting

### Common Issues

1. **Elasticsearch Connection Error**
   ```bash
   # Check Elasticsearch status
   curl http://localhost:9200/_cluster/health
   
   # Restart if needed
   docker-compose restart elasticsearch
   ```

2. **Database Migration Issues**
   ```bash
   # Reset database
   invenio-cli services destroy
   invenio-cli services setup
   ```

3. **Asset Build Failures**
   ```bash
   # Clean and rebuild assets
   invenio-cli assets clean
   invenio-cli assets build
   ```

4. **DOI Minting Failures**
   - Verify DataCite credentials
   - Check test mode settings
   - Review DataCite API logs

### Performance Optimization

1. **Database Indexing**
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_records_created ON records_metadata(created);
   CREATE INDEX idx_records_updated ON records_metadata(updated);
   ```

2. **Elasticsearch Tuning**
   ```yaml
   # Increase heap size for production
   environment:
     - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
   ```

3. **Redis Configuration**
   ```bash
   # Optimize Redis for caching
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   ```

This setup guide provides a complete foundation for running InvenioRDM with DRH-specific configurations and Make Data Count integration capabilities.
