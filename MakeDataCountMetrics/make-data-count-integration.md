# Make Data Count Integration for Diabetes Research Hub (DRH)

## Overview

This document outlines the integration of Make Data Count metrics into the Diabetes Research Hub (DRH) project to track use, re-use, and understand the impact of DRH resources. Make Data Count is an initiative that promotes the development of open data metrics to enable evaluation of data usage in research environments.

## What is Make Data Count?

Make Data Count is a community-driven initiative that:
- Promotes development of open data metrics to enable evaluation of data usage
- Provides open infrastructure for measuring data impact
- Drives awareness and adoption of data metrics
- Builds evidence on the usage and impact of open data

### Key Components:
1. **Data Citations**: Structured references to data as part of scholarly work
2. **Usage Metrics**: Normalized counts of views, downloads, and access patterns
3. **Impact Assessment**: Qualitative and quantitative measures of data utilization
4. **Open Infrastructure**: Community-led transparent metrics collection

## Current DRH Analytics Capabilities

The DRH project already has several analytics and tracking mechanisms in place:

### Existing Infrastructure:
- **OpenTelemetry Integration**: Comprehensive observability with metrics, logging, and tracing
- **Activity Logging**: Detailed user interaction tracking via `ActivityLogService`
- **Session Management**: User session tracking and audit trails
- **Database Access Monitoring**: SQL query logging and performance metrics
- **Authentication Tracking**: Login/logout events and user verification
- **Research Study Metrics**: CGM data analysis, participant metrics, and study statistics

### Current Tracking Capabilities:
- User interactions and page views
- Data access patterns and query execution
- Research study utilization
- CGM data processing and analysis
- Participant engagement metrics
- Study metadata and demographics

## Make Data Count Integration Strategy

### Phase 1: Data Citation Implementation

#### 1.1 DOI Assignment for DRH Datasets
```yaml
# Add to application.yml
org:
  diabetestechnology:
    drh:
      datacite:
        enabled: true
        repository-id: "drh.diabetes-research"
        prefix: "10.5555"  # Example prefix
        metadata-endpoint: "https://api.datacite.org/dois"
        username: "${DATACITE_USERNAME}"
        password: "${DATACITE_PASSWORD}"
```

#### 1.2 Dataset Registration Service
Create a new service to register research datasets with DataCite:

```java
@Service
public class DataCiteRegistrationService {
    
    public void registerStudyDataset(String studyId, StudyMetadata metadata) {
        // Generate DOI for study dataset
        // Submit metadata to DataCite
        // Store DOI in database
    }
    
    public void registerParticipantDataset(String studyId, String participantId) {
        // Register individual participant datasets
    }
}
```

#### 1.3 Citation Tracking
Implement citation detection and tracking:

```java
@Entity
public class DataCitation {
    private String doi;
    private String citingWork;
    private String citationType;
    private LocalDateTime citationDate;
    private String studyId;
    private String participantId;
}
```

### Phase 2: Usage Metrics Collection

#### 2.1 COUNTER Code of Practice Implementation
Implement COUNTER-compliant usage tracking:

```java
@Component
public class CounterMetricsCollector {
    
    public void recordDatasetView(String doi, String userAgent, String ipAddress) {
        // Record dataset view per COUNTER standards
    }
    
    public void recordDatasetDownload(String doi, String format, String userAgent) {
        // Record dataset download per COUNTER standards
    }
}
```

#### 2.2 Enhanced Activity Logging
Extend existing `ActivityLogService` to capture data-specific metrics:

```java
public class DataUsageMetrics {
    private String datasetDoi;
    private String accessType; // view, download, analysis
    private String userType; // researcher, clinician, student
    private String institutionId;
    private Map<String, Object> analysisParameters;
    private Duration sessionDuration;
}
```

### Phase 3: Impact Assessment Framework

#### 3.1 Research Impact Tracking
```java
@Service
public class ResearchImpactService {
    
    public ImpactMetrics calculateStudyImpact(String studyId) {
        return ImpactMetrics.builder()
            .citationCount(getCitationCount(studyId))
            .downloadCount(getDownloadCount(studyId))
            .reuseCount(getReuseCount(studyId))
            .collaborationCount(getCollaborationCount(studyId))
            .build();
    }
}
```

#### 3.2 Data Reuse Detection
Implement algorithms to detect data reuse patterns:

```java
public class DataReuseDetector {
    
    public List<ReuseEvent> detectReuse(String originalStudyId) {
        // Analyze similar analysis patterns
        // Detect derivative datasets
        // Identify cross-study comparisons
    }
}
```

## Implementation Plan

### Phase 1: Foundation (Weeks 1-4)
1. **Setup DataCite Integration**
   - Configure DataCite API credentials
   - Implement DOI minting service
   - Create dataset metadata schema

2. **Extend Database Schema**
   ```sql
   CREATE TABLE dataset_dois (
       id SERIAL PRIMARY KEY,
       study_id VARCHAR(255),
       participant_id VARCHAR(255),
       doi VARCHAR(255) UNIQUE,
       metadata JSONB,
       created_at TIMESTAMP DEFAULT NOW()
   );
   
   CREATE TABLE data_citations (
       id SERIAL PRIMARY KEY,
       dataset_doi VARCHAR(255),
       citing_work VARCHAR(500),
       citation_type VARCHAR(100),
       detected_at TIMESTAMP DEFAULT NOW()
   );
   ```

3. **Basic Usage Tracking**
   - Implement COUNTER-compliant logging
   - Extend existing activity logging
   - Create usage metrics aggregation

### Phase 2: Advanced Metrics (Weeks 5-8)
1. **Citation Detection**
   - Integrate with scholarly databases
   - Implement citation parsing
   - Create citation notification system

2. **Impact Visualization**
   - Create impact dashboard components
   - Integrate with existing web components
   - Add metrics to study detail pages

3. **API Endpoints**
   ```java
   @RestController
   @RequestMapping("/api/metrics")
   public class MetricsController {
       
       @GetMapping("/study/{studyId}/impact")
       public ResponseEntity<ImpactMetrics> getStudyImpact(@PathVariable String studyId) {
           // Return comprehensive impact metrics
       }
       
       @GetMapping("/dataset/{doi}/usage")
       public ResponseEntity<UsageMetrics> getDatasetUsage(@PathVariable String doi) {
           // Return usage statistics
       }
   }
   ```

### Phase 3: Integration & Reporting (Weeks 9-12)
1. **Dashboard Integration**
   - Add Make Data Count metrics to existing dashboards
   - Create dedicated impact reporting pages
   - Implement real-time metrics updates

2. **Automated Reporting**
   - Generate monthly impact reports
   - Create researcher notification system
   - Implement funder reporting capabilities

3. **Community Integration**
   - Submit usage reports to DataCite
   - Participate in Make Data Count corpus
   - Enable data citation corpus contributions

## Technical Architecture

### Data Flow
```mermaid
graph TD
    A[User Interaction] --> B[Activity Logger]
    B --> C[Usage Metrics Collector]
    C --> D[COUNTER Processor]
    D --> E[DataCite Submission]
    
    F[Research Dataset] --> G[DOI Assignment]
    G --> H[Metadata Registration]
    H --> I[Citation Tracking]
    
    J[External Citations] --> K[Citation Detector]
    K --> L[Impact Calculator]
    L --> M[Dashboard Display]
```

### Integration Points
1. **Existing Activity Logging**: Extend `ActivityLogService` for data-specific events
2. **OpenTelemetry**: Add custom metrics for data usage tracking
3. **Database Layer**: Utilize existing jOOQ setup for metrics storage
4. **Web Components**: Create new components for metrics visualization
5. **API Layer**: Extend existing REST APIs with metrics endpoints

## Configuration

### Environment Variables
```bash
# DataCite Configuration
DATACITE_API_URL=https://api.datacite.org
DATACITE_USERNAME=your_username
DATACITE_PASSWORD=your_password
DATACITE_REPOSITORY_ID=drh.diabetes-research

# Make Data Count Settings
MDC_ENABLED=true
MDC_REPORTING_INTERVAL=daily
MDC_CITATION_DETECTION=true
```

### Application Properties
```yaml
org:
  diabetestechnology:
    drh:
      make-data-count:
        enabled: true
        datacite:
          api-url: ${DATACITE_API_URL}
          username: ${DATACITE_USERNAME}
          password: ${DATACITE_PASSWORD}
        metrics:
          collection-interval: PT1H
          reporting-enabled: true
        citations:
          detection-enabled: true
          sources:
            - pubmed
            - crossref
            - semantic-scholar
```

## Benefits for DRH

### For Researchers
- **Recognition**: Proper citation and credit for data contributions
- **Impact Visibility**: Clear metrics on data usage and influence
- **Collaboration Opportunities**: Discovery of researchers using similar data

### For Institutions
- **Funding Justification**: Evidence of research data impact
- **Compliance**: Meeting funder requirements for data sharing metrics
- **Strategic Planning**: Understanding which datasets drive most value

### For the Community
- **Transparency**: Open metrics on data usage patterns
- **Reproducibility**: Better tracking of data provenance and reuse
- **Innovation**: Insights into effective data sharing practices

## Next Steps

1. **Review and Approval**: Stakeholder review of integration plan
2. **Resource Allocation**: Assign development team and timeline
3. **Pilot Implementation**: Start with Phase 1 on a subset of studies
4. **Community Engagement**: Connect with Make Data Count initiative
5. **Iterative Development**: Implement feedback and expand capabilities

## Resources and References

- [Make Data Count Website](https://makedatacount.org/)
- [DataCite API Documentation](https://support.datacite.org/docs/api)
- [COUNTER Code of Practice for Research Data](https://www.projectcounter.org/code-of-practice-rd-sections/)
- [Data Citation Corpus](https://zenodo.org/records/11216814)
- [OpenTelemetry Metrics](https://opentelemetry.io/docs/concepts/signals/metrics/)

---

*This document serves as a comprehensive guide for integrating Make Data Count metrics into the DRH project. Regular updates will be made as the implementation progresses and new requirements emerge.*
