# Make Data Count API Specification for DRH

## Overview

This document defines the REST API endpoints for Make Data Count metrics integration in the Diabetes Research Hub (DRH) project.

## Base URL

```
https://your-drh-domain.com/api/metrics
```

## Authentication

All API endpoints require authentication. Use the same authentication mechanism as other DRH APIs.

## Endpoints

### 1. Study Impact Metrics

#### GET /study/{studyId}/impact

Retrieve comprehensive impact metrics for a specific study.

**Parameters:**
- `studyId` (path): Study identifier
- `period` (query, optional): Time period for metrics (`daily`, `monthly`, `yearly`, `all`)
- `startDate` (query, optional): Start date for custom period (ISO 8601)
- `endDate` (query, optional): End date for custom period (ISO 8601)

**Response:**
```json
{
  "studyId": "study-001",
  "doi": "10.5555/drh-study-001-1234567890",
  "title": "Diabetes CGM Study Dataset",
  "period": "monthly",
  "metrics": {
    "viewCount": 1250,
    "downloadCount": 89,
    "citationCount": 12,
    "uniqueUsers": 234,
    "uniqueInstitutions": 45,
    "countriesReached": 23,
    "lastUpdated": "2024-01-15T10:30:00Z"
  },
  "trends": {
    "viewTrend": "+15%",
    "downloadTrend": "+8%",
    "citationTrend": "+2"
  },
  "topCountries": [
    {"country": "US", "count": 456},
    {"country": "UK", "count": 234},
    {"country": "CA", "count": 123}
  ]
}
```

#### GET /study/{studyId}/usage-timeline

Get usage metrics over time for visualization.

**Parameters:**
- `studyId` (path): Study identifier
- `granularity` (query): Time granularity (`hour`, `day`, `week`, `month`)
- `startDate` (query): Start date (ISO 8601)
- `endDate` (query): End date (ISO 8601)

**Response:**
```json
{
  "studyId": "study-001",
  "granularity": "day",
  "timeline": [
    {
      "date": "2024-01-01",
      "views": 45,
      "downloads": 3,
      "uniqueUsers": 12
    },
    {
      "date": "2024-01-02",
      "views": 52,
      "downloads": 5,
      "uniqueUsers": 15
    }
  ]
}
```

### 2. Dataset Management

#### POST /dataset/register

Register a new dataset and mint a DOI.

**Request Body:**
```json
{
  "studyId": "study-001",
  "participantId": "participant-123", // optional
  "datasetType": "study", // "study", "participant", "aggregate"
  "title": "Diabetes CGM Study Dataset",
  "description": "Comprehensive CGM data from diabetes study",
  "creators": [
    {
      "name": "Dr. Jane Smith",
      "affiliation": "University Hospital",
      "orcid": "0000-0000-0000-0000"
    }
  ],
  "publicationYear": 2024,
  "resourceType": "Dataset",
  "subjects": ["diabetes", "CGM", "glucose monitoring"],
  "metadata": {
    "participantCount": 150,
    "dataPoints": 2500000,
    "studyDuration": "12 months"
  }
}
```

**Response:**
```json
{
  "success": true,
  "doi": "10.5555/drh-study-001-1234567890",
  "url": "https://doi.org/10.5555/drh-study-001-1234567890",
  "status": "published",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

#### PUT /dataset/{doi}/metadata

Update dataset metadata.

**Parameters:**
- `doi` (path): Dataset DOI

**Request Body:**
```json
{
  "title": "Updated Dataset Title",
  "description": "Updated description",
  "metadata": {
    "participantCount": 175,
    "dataPoints": 3000000
  }
}
```

### 3. Citation Management

#### GET /dataset/{doi}/citations

Retrieve all citations for a dataset.

**Parameters:**
- `doi` (path): Dataset DOI
- `verified` (query, optional): Filter by verification status
- `source` (query, optional): Filter by citation source

**Response:**
```json
{
  "doi": "10.5555/drh-study-001-1234567890",
  "totalCitations": 12,
  "citations": [
    {
      "id": 1,
      "citingWorkDoi": "10.1000/journal.article.123",
      "citingWorkTitle": "Analysis of CGM Data Patterns",
      "authors": ["Dr. John Doe", "Dr. Jane Smith"],
      "citationType": "direct",
      "citationContext": "We used the diabetes dataset from...",
      "detectedAt": "2024-01-10T14:20:00Z",
      "verified": true,
      "source": "crossref"
    }
  ]
}
```

#### POST /dataset/{doi}/citations

Manually add a citation.

**Parameters:**
- `doi` (path): Dataset DOI

**Request Body:**
```json
{
  "citingWorkDoi": "10.1000/journal.article.456",
  "citingWorkTitle": "Diabetes Research Study",
  "authors": ["Dr. Alice Johnson"],
  "citationType": "indirect",
  "citationContext": "Referenced methodology from...",
  "source": "manual"
}
```

### 4. Usage Analytics

#### GET /usage/summary

Get overall usage summary across all datasets.

**Parameters:**
- `period` (query): Time period (`daily`, `weekly`, `monthly`, `yearly`)
- `startDate` (query, optional): Start date
- `endDate` (query, optional): End date

**Response:**
```json
{
  "period": "monthly",
  "summary": {
    "totalDatasets": 45,
    "totalViews": 15420,
    "totalDownloads": 1234,
    "totalCitations": 89,
    "activeUsers": 567,
    "newUsers": 123
  },
  "topDatasets": [
    {
      "doi": "10.5555/drh-study-001-1234567890",
      "title": "Diabetes CGM Study Dataset",
      "views": 1250,
      "downloads": 89
    }
  ]
}
```

#### GET /usage/geographic

Get geographic distribution of usage.

**Response:**
```json
{
  "totalCountries": 45,
  "distribution": [
    {
      "countryCode": "US",
      "countryName": "United States",
      "views": 5420,
      "downloads": 234,
      "uniqueUsers": 123
    },
    {
      "countryCode": "UK",
      "countryName": "United Kingdom", 
      "views": 2340,
      "downloads": 156,
      "uniqueUsers": 89
    }
  ]
}
```

### 5. Reporting

#### GET /reports/impact/{studyId}

Generate comprehensive impact report for a study.

**Parameters:**
- `studyId` (path): Study identifier
- `format` (query): Report format (`json`, `pdf`, `csv`)
- `period` (query): Reporting period

**Response (JSON format):**
```json
{
  "reportId": "report-001-20240115",
  "studyId": "study-001",
  "generatedAt": "2024-01-15T10:30:00Z",
  "period": "2023-01-01 to 2023-12-31",
  "summary": {
    "totalViews": 15420,
    "totalDownloads": 1234,
    "totalCitations": 89,
    "impactScore": 8.5,
    "reachScore": 7.2
  },
  "details": {
    "monthlyBreakdown": [...],
    "geographicDistribution": [...],
    "citationAnalysis": [...],
    "userEngagement": [...]
  }
}
```

#### POST /reports/schedule

Schedule automated report generation.

**Request Body:**
```json
{
  "studyId": "study-001",
  "reportType": "impact",
  "frequency": "monthly", // "daily", "weekly", "monthly", "quarterly"
  "format": "pdf",
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "startDate": "2024-02-01"
}
```

### 6. Configuration

#### GET /config/datacite

Get DataCite configuration status.

**Response:**
```json
{
  "enabled": true,
  "repositoryId": "drh.diabetes-research",
  "prefix": "10.5555",
  "status": "connected",
  "lastSync": "2024-01-15T09:00:00Z"
}
```

#### GET /config/metrics

Get metrics collection configuration.

**Response:**
```json
{
  "collectionEnabled": true,
  "counterCompliant": true,
  "collectionInterval": "PT1H",
  "reportingEnabled": true,
  "citationDetection": {
    "enabled": true,
    "sources": ["crossref", "pubmed", "datacite"],
    "lastRun": "2024-01-15T08:00:00Z"
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "DATASET_NOT_FOUND",
    "message": "Dataset with DOI 10.5555/example not found",
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/metrics/dataset/10.5555/example/citations"
  }
}
```

### Common Error Codes

- `DATASET_NOT_FOUND`: Dataset does not exist
- `DOI_MINTING_FAILED`: Failed to mint DOI with DataCite
- `INVALID_PARAMETERS`: Invalid request parameters
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `EXTERNAL_SERVICE_ERROR`: External service (DataCite, etc.) unavailable

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Standard endpoints**: 100 requests per minute per user
- **Report generation**: 10 requests per hour per user
- **DOI minting**: 50 requests per day per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## Webhooks

### Citation Detection Webhook

When new citations are detected, a webhook can be triggered:

**POST** to configured webhook URL:
```json
{
  "event": "citation_detected",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "datasetDoi": "10.5555/drh-study-001-1234567890",
    "citingWorkDoi": "10.1000/journal.article.789",
    "citationType": "direct",
    "source": "crossref"
  }
}
```

### Usage Milestone Webhook

Triggered when usage milestones are reached:

```json
{
  "event": "usage_milestone",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "datasetDoi": "10.5555/drh-study-001-1234567890",
    "milestone": "1000_views",
    "currentCount": 1000,
    "studyId": "study-001"
  }
}
```

## SDK and Client Libraries

### JavaScript/TypeScript Client

```typescript
import { DRHMetricsClient } from '@drh/metrics-client';

const client = new DRHMetricsClient({
  baseUrl: 'https://your-drh-domain.com/api/metrics',
  apiKey: 'your-api-key'
});

// Get study impact metrics
const metrics = await client.getStudyImpact('study-001');

// Register new dataset
const doi = await client.registerDataset({
  studyId: 'study-001',
  title: 'My Dataset',
  description: 'Dataset description'
});
```

### Python Client

```python
from drh_metrics import DRHMetricsClient

client = DRHMetricsClient(
    base_url='https://your-drh-domain.com/api/metrics',
    api_key='your-api-key'
)

# Get study impact metrics
metrics = client.get_study_impact('study-001')

# Register new dataset
doi = client.register_dataset(
    study_id='study-001',
    title='My Dataset',
    description='Dataset description'
)
```

## OpenAPI Specification

The complete OpenAPI 3.0 specification is available at:
```
https://your-drh-domain.com/api/metrics/openapi.json
```

Interactive API documentation (Swagger UI):
```
https://your-drh-domain.com/api/metrics/docs
```

---

This API specification provides comprehensive endpoints for integrating Make Data Count metrics into the DRH project, enabling full tracking and analysis of dataset usage and impact.
